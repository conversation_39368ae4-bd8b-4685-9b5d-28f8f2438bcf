# Agent Onboarding API - Deployment Guide

## Prerequisites

### System Requirements
- Node.js 18.0.0 or higher
- npm 9.0.0 or higher
- PostgreSQL 12 or higher

### Development Tools (Optional)
- Git
- Postman or similar API testing tool
- pgAdmin or similar PostgreSQL management tool

## Installation Steps

### 1. Clone and Setup Project
```bash
# Clone the repository
git clone <repository-url>
cd agentonboarding-api

# Install dependencies
npm install
```

### 2. Database Setup

#### Install PostgreSQL
**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**macOS (using Homebrew):**
```bash
brew install postgresql
brew services start postgresql
```

**Windows:**
Download and install from [PostgreSQL official website](https://www.postgresql.org/download/windows/)

#### Create Database and User
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE agentonboarding_db;
CREATE USER agentonboarding_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE agentonboarding_db TO agentonboarding_user;
\q
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

**Required Environment Variables:**
```env
NODE_ENV=development
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=agentonboarding_db
DB_USERNAME=agentonboarding_user
DB_PASSWORD=your_secure_password

# JWT Configuration (Generate a strong secret)
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
```

### 4. Database Migration and Seeding
```bash
# Run the automated setup
npm run setup

# Or manually:
npm run migrate
npm run seed  # Optional: adds sample data
```

### 5. Start the Server
```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

## Production Deployment

### 1. Environment Setup
```env
NODE_ENV=production
PORT=3000

# Database (use production credentials)
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=agentonboarding_prod
DB_USERNAME=prod_user
DB_PASSWORD=secure_production_password
DB_SSL=true

# JWT (use strong secrets)
JWT_SECRET=very-long-random-string-for-production-at-least-64-characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Security
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 2. Process Management (PM2)
```bash
# Install PM2 globally
npm install -g pm2

# Start application with PM2
pm2 start server.js --name "agentonboarding-api"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

### 3. Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-api-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-api-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Docker Deployment

### 1. Create Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

### 2. Create docker-compose.yml
```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_NAME=agentonboarding_db
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
    depends_on:
      - postgres
    volumes:
      - .:/app
      - /app/node_modules

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=agentonboarding_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

### 3. Run with Docker
```bash
# Build and start
docker-compose up -d

# Run migrations
docker-compose exec api npm run migrate

# Run seeders (optional)
docker-compose exec api npm run seed
```

## Monitoring and Logging

### 1. Application Logs
```bash
# View logs with PM2
pm2 logs agentonboarding-api

# View logs with Docker
docker-compose logs -f api
```

### 2. Health Monitoring
```bash
# Health check endpoint
curl http://localhost:3000/api/v1/health

# PM2 monitoring
pm2 monit
```

## Security Checklist

- [ ] Use strong JWT secrets (64+ characters)
- [ ] Enable SSL/TLS in production
- [ ] Configure proper CORS origins
- [ ] Set up rate limiting
- [ ] Use environment variables for secrets
- [ ] Enable database SSL in production
- [ ] Regular security updates
- [ ] Implement proper logging
- [ ] Set up monitoring and alerts
- [ ] Use strong database passwords
- [ ] Restrict database access
- [ ] Enable firewall rules

## Backup Strategy

### Database Backup
```bash
# Create backup
pg_dump -h localhost -U agentonboarding_user agentonboarding_db > backup.sql

# Restore backup
psql -h localhost -U agentonboarding_user agentonboarding_db < backup.sql

# Automated daily backup
echo "0 2 * * * pg_dump -h localhost -U agentonboarding_user agentonboarding_db > /backups/db_$(date +\%Y\%m\%d).sql" | crontab -
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify credentials in .env
   - Check firewall settings

2. **Port Already in Use**
   ```bash
   # Find process using port 3000
   lsof -i :3000
   # Kill process
   kill -9 <PID>
   ```

3. **Migration Errors**
   ```bash
   # Reset migrations
   npm run migrate:undo
   npm run migrate
   ```

4. **Permission Errors**
   - Check file permissions
   - Ensure proper user ownership
   - Verify database user permissions

### Logs Location
- Application logs: PM2 logs or console output
- Database logs: PostgreSQL log directory
- System logs: `/var/log/`

## Performance Optimization

1. **Database Indexing**: Already included in migrations
2. **Connection Pooling**: Configured in Sequelize
3. **Compression**: Enabled in Express
4. **Rate Limiting**: Configured
5. **Caching**: Consider Redis for session storage

## Maintenance

### Regular Tasks
- Update dependencies: `npm audit fix`
- Database maintenance: `VACUUM ANALYZE`
- Log rotation: Configure logrotate
- Security updates: Keep OS and packages updated
- Monitor disk space and performance
- Review audit logs regularly

### Scaling Considerations
- Use load balancer for multiple instances
- Implement database read replicas
- Consider microservices architecture
- Use Redis for session management
- Implement caching strategies
