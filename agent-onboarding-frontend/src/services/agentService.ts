import { apiService } from './api';

export interface Agent {
  id: string;
  application_number: string;
  sales_code?: string;
  first_name: string;
  last_name: string;
  nic_number: string;
  email: string;
  phone_number: string;
  date_of_birth: string;
  address: string;
  designation: string;
  status: 'DRAFT' | 'PENDING_SUBMISSION' | 'SUBMITTED' | 'APPROVED' | 'REJECTED';
  rejection_reason?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface AgentFilters {
  status?: string[];
  nic_number?: string;
  sales_code?: string;
  application_number?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

class AgentService {
  async getAgents(filters: AgentFilters = {}): Promise<PaginatedResponse<Agent>> {
    const params: any = {};
    
    if (filters.status && filters.status.length > 0) {
      params.status = filters.status.join(',');
    }
    if (filters.nic_number) params.nic_number = filters.nic_number;
    if (filters.sales_code) params.sales_code = filters.sales_code;
    if (filters.application_number) params.application_number = filters.application_number;
    if (filters.search) params.search = filters.search;
    if (filters.page) params.page = filters.page;
    if (filters.limit) params.limit = filters.limit;

    return apiService.get('/agents', params);
  }

  async getAgent(id: string): Promise<ApiResponse<Agent>> {
    return apiService.get(`/agents/${id}`);
  }

  async createAgent(agentData: Omit<Agent, 'id' | 'application_number' | 'sales_code' | 'status' | 'created_by' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Agent>> {
    return apiService.post('/agents', agentData);
  }

  async updateAgent(id: string, agentData: Partial<Agent>): Promise<ApiResponse<Agent>> {
    return apiService.put(`/agents/${id}`, agentData);
  }

  async deleteAgent(id: string): Promise<ApiResponse<void>> {
    return apiService.delete(`/agents/${id}`);
  }

  async submitAgent(id: string): Promise<ApiResponse<Agent>> {
    return apiService.post(`/agents/${id}/submit`);
  }

  async approveAgent(id: string): Promise<ApiResponse<Agent>> {
    return apiService.post(`/agents/${id}/approve`);
  }

  async rejectAgent(id: string, rejection_reason: string): Promise<ApiResponse<Agent>> {
    return apiService.post(`/agents/${id}/reject`, { rejection_reason });
  }

  async getApprovalTray(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Agent>> {
    return apiService.get('/agents/approval-tray', { page, limit });
  }
}

export const agentService = new AgentService();
