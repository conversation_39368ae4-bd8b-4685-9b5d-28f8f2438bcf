import { apiService } from './api';
import { User } from '../contexts/AuthContext';

interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    user: User;
  };
}

interface ProfileResponse {
  success: boolean;
  message: string;
  data: User;
}

class AuthService {
  async login(email: string, password: string): Promise<{ token: string; user: User }> {
    const response: LoginResponse = await apiService.post('/auth/login', {
      email,
      password,
    });

    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || 'Login failed');
    }
  }

  async logout(): Promise<void> {
    await apiService.post('/auth/logout');
  }

  async getProfile(): Promise<User> {
    const response: ProfileResponse = await apiService.get('/auth/profile');

    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || 'Failed to get profile');
    }
  }

  async register(userData: {
    user_id: string;
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    role: string;
    branch_code: string;
  }): Promise<User> {
    const response: any = await apiService.post('/auth/register', userData);

    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message || 'Registration failed');
    }
  }
}

export const authService = new AuthService();
