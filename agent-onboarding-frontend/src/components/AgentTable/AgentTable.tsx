import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Box,
  Typography,
  Pagination,
} from '@mui/material';

import { Agent } from '../../services/agentService';
import { statusColors } from '../../theme/theme';

interface AgentTableProps {
  agents: Agent[];
  onEdit: (agent: Agent) => void;
  onDelete: (agent: Agent) => void;
  onSubmit: (agent: Agent) => void;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  onPageChange: (page: number) => void;
  userPermissions: any;
}

const AgentTable: React.FC<AgentTableProps> = ({
  agents,
  onEdit,
  onDelete,
  onSubmit,
  pagination,
  onPageChange,
  userPermissions,
}) => {
  const getStatusChip = (status: Agent['status']) => {
    const statusLabels = {
      DRAFT: 'Draft',
      PENDING_SUBMISSION: 'Pending Submission',
      SUBMITTED: 'Submitted',
      APPROVED: 'Approved',
      REJECTED: 'Rejected',
    };

    return (
      <Chip
        label={statusLabels[status]}
        size="small"
        sx={{
          backgroundColor: statusColors[status],
          color: 'white',
          fontWeight: 500,
        }}
      />
    );
  };

  const canEdit = (agent: Agent) => {
    return userPermissions?.canEdit &&
      (agent.status === 'DRAFT' || agent.status === 'PENDING_SUBMISSION');
  };

  const canDelete = (agent: Agent) => {
    return userPermissions?.canDelete && !agent.sales_code;
  };

  const canSubmit = (agent: Agent) => {
    return agent.status === 'PENDING_SUBMISSION';
  };

  if (agents.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No agents found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Try adjusting your filters or create a new application
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 600, backgroundColor: '#f8f9fa' }}>Application #</TableCell>
              <TableCell sx={{ fontWeight: 600, backgroundColor: '#f8f9fa' }}>Sales Code</TableCell>
              <TableCell sx={{ fontWeight: 600, backgroundColor: '#f8f9fa' }}>Applicant Name</TableCell>
              <TableCell sx={{ fontWeight: 600, backgroundColor: '#f8f9fa' }}>Designation</TableCell>
              <TableCell sx={{ fontWeight: 600, backgroundColor: '#f8f9fa' }}>Status</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, backgroundColor: '#f8f9fa' }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {agents.map((agent) => (
              <TableRow key={agent.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight={500}>
                    {agent.application_number}
                  </Typography>
                </TableCell>
                <TableCell>
                  {agent.sales_code ? (
                    <Typography variant="body2" fontWeight={500}>
                      {agent.sales_code}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      -
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {agent.first_name} {agent.last_name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {agent.email}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {agent.designation}
                  </Typography>
                </TableCell>
                <TableCell>
                  {getStatusChip(agent.status)}
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
                    {canEdit(agent) ? (
                      <Button
                        size="small"
                        onClick={() => onEdit(agent)}
                        sx={{
                          textTransform: 'none',
                          minWidth: 'auto',
                          px: 1,
                          py: 0.5,
                          fontSize: '0.75rem',
                          color: 'primary.main',
                          '&:hover': {
                            backgroundColor: 'primary.50',
                          },
                        }}
                      >
                        Edit
                      </Button>
                    ) : (
                      <Button
                        size="small"
                        onClick={() => onEdit(agent)}
                        sx={{
                          textTransform: 'none',
                          minWidth: 'auto',
                          px: 1,
                          py: 0.5,
                          fontSize: '0.75rem',
                          color: 'success.main',
                          '&:hover': {
                            backgroundColor: 'success.50',
                          },
                        }}
                      >
                        View
                      </Button>
                    )}

                    {canDelete(agent) && (
                      <Button
                        size="small"
                        onClick={() => onDelete(agent)}
                        sx={{
                          textTransform: 'none',
                          minWidth: 'auto',
                          px: 1,
                          py: 0.5,
                          fontSize: '0.75rem',
                          color: 'error.main',
                          '&:hover': {
                            backgroundColor: 'error.50',
                          },
                        }}
                      >
                        Delete
                      </Button>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: 2,
        borderTop: '1px solid #e0e0e0',
        backgroundColor: '#fafafa'
      }}>
        <Typography variant="body2" color="text.secondary">
          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
          {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
          {pagination.total} entries
        </Typography>
        <Pagination
          count={pagination.totalPages}
          page={pagination.page}
          onChange={(_, page) => onPageChange(page)}
          color="primary"
          size="small"
          showFirstButton
          showLastButton
        />
      </Box>
    </Box>
  );
};

export default AgentTable;
