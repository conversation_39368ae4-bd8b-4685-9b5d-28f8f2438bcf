import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Box,
  Typography,
  Pagination,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Send as SubmitIcon,
} from '@mui/icons-material';
import { Agent } from '../../services/agentService';
import { statusColors } from '../../theme/theme';

interface AgentTableProps {
  agents: Agent[];
  onEdit: (agent: Agent) => void;
  onDelete: (agent: Agent) => void;
  onSubmit: (agent: Agent) => void;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  onPageChange: (page: number) => void;
  userPermissions: any;
}

const AgentTable: React.FC<AgentTableProps> = ({
  agents,
  onEdit,
  onDelete,
  onSubmit,
  pagination,
  onPageChange,
  userPermissions,
}) => {
  const getStatusChip = (status: Agent['status']) => {
    const statusLabels = {
      DRAFT: 'Draft',
      PENDING_SUBMISSION: 'Pending Submission',
      SUBMITTED: 'Submitted',
      APPROVED: 'Approved',
      REJECTED: 'Rejected',
    };

    return (
      <Chip
        label={statusLabels[status]}
        size="small"
        sx={{
          backgroundColor: statusColors[status],
          color: 'white',
          fontWeight: 500,
        }}
      />
    );
  };

  const canEdit = (agent: Agent) => {
    return userPermissions?.canEdit &&
      (agent.status === 'DRAFT' || agent.status === 'PENDING_SUBMISSION');
  };

  const canDelete = (agent: Agent) => {
    return userPermissions?.canDelete && !agent.sales_code;
  };

  const canSubmit = (agent: Agent) => {
    return agent.status === 'PENDING_SUBMISSION';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (agents.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No agents found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Try adjusting your filters or create a new application
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <TableContainer component={Paper} elevation={0}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Application #</TableCell>
              <TableCell>Sales Code</TableCell>
              <TableCell>Applicant Name</TableCell>
              <TableCell>Designation</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created Date</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {agents.map((agent) => (
              <TableRow key={agent.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight={500}>
                    {agent.application_number}
                  </Typography>
                </TableCell>
                <TableCell>
                  {agent.sales_code ? (
                    <Typography variant="body2" fontWeight={500}>
                      {agent.sales_code}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      -
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {agent.first_name} {agent.last_name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {agent.email}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {agent.designation}
                  </Typography>
                </TableCell>
                <TableCell>
                  {getStatusChip(agent.status)}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(agent.created_at)}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                    {canEdit(agent) ? (
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => onEdit(agent)}
                          color="primary"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <Tooltip title="View">
                        <IconButton
                          size="small"
                          onClick={() => onEdit(agent)}
                          color="default"
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {canDelete(agent) && (
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          onClick={() => onDelete(agent)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {canSubmit(agent) && (
                      <Tooltip title="Submit for Approval">
                        <IconButton
                          size="small"
                          onClick={() => onSubmit(agent)}
                          color="success"
                        >
                          <SubmitIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
        <Typography variant="body2" color="text.secondary">
          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
          {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
          {pagination.total} entries
        </Typography>
        <Pagination
          count={pagination.totalPages}
          page={pagination.page}
          onChange={(_, page) => onPageChange(page)}
          color="primary"
          showFirstButton
          showLastButton
        />
      </Box>
    </Box>
  );
};

export default AgentTable;
