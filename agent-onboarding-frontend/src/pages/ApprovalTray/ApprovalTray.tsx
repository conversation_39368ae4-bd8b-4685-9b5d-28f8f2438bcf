import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Pagination,
  IconButton,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  ArrowBack as BackIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { agentService, Agent } from '../../services/agentService';
import { statusColors } from '../../theme/theme';
import { useAuth } from '../../contexts/AuthContext';

const ApprovalTray: React.FC = () => {
  const navigate = useNavigate();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processing, setProcessing] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  useEffect(() => {
    fetchApprovalTray();
  }, [pagination.page]);

  const fetchApprovalTray = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      const response = await agentService.getApprovalTray(pagination.page, pagination.limit);

      if (response.success) {
        setAgents(response.data);
        setPagination(response.pagination);
      } else {
        setError('Failed to fetch approval tray');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch approval tray');
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit]);

  const handleApprove = async (agent: Agent) => {
    if (!window.confirm(`Are you sure you want to approve ${agent.first_name} ${agent.last_name}'s application?`)) {
      return;
    }

    try {
      setProcessing(true);
      const response = await agentService.approveAgent(agent.id);

      if (response.success) {
        setSuccess(`Application approved successfully. Sales Code: ${response.data.sales_code}`);
        fetchApprovalTray();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to approve application');
    } finally {
      setProcessing(false);
    }
  };

  const handleRejectClick = (agent: Agent) => {
    setSelectedAgent(agent);
    setRejectDialogOpen(true);
    setRejectionReason('');
  };

  const handleRejectConfirm = async () => {
    if (!selectedAgent || !rejectionReason.trim()) {
      setError('Rejection reason is required');
      return;
    }

    try {
      setProcessing(true);
      const response = await agentService.rejectAgent(selectedAgent.id, rejectionReason.trim());

      if (response.success) {
        setSuccess('Application rejected successfully');
        setRejectDialogOpen(false);
        setSelectedAgent(null);
        setRejectionReason('');
        fetchApprovalTray();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to reject application');
    } finally {
      setProcessing(false);
    }
  };

  const handleView = (agent: Agent) => {
    navigate(`/agents/${agent.id}/edit`);
  };

  const handleBack = () => {
    navigate('/dashboard');
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusChip = (status: Agent['status']) => {
    const statusLabels = {
      DRAFT: 'Draft',
      PENDING_SUBMISSION: 'Pending Submission',
      SUBMITTED: 'Submitted',
      APPROVED: 'Approved',
      REJECTED: 'Rejected',
    };

    return (
      <Chip
        label={statusLabels[status]}
        size="small"
        sx={{
          backgroundColor: statusColors[status],
          color: 'white',
          fontWeight: 500,
        }}
      />
    );
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<BackIcon />}
          onClick={handleBack}
          sx={{ mr: 2 }}
        >
          Back to Dashboard
        </Button>
        <Typography variant="h4" fontWeight="600">
          Approval Tray
        </Typography>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="primary" fontWeight="bold">
                {pagination.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Pending
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {agents.filter(a => a.status === 'APPROVED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Approved Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="error.main" fontWeight="bold">
                {agents.filter(a => a.status === 'REJECTED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Rejected Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {agents.filter(a => a.status === 'SUBMITTED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Awaiting Review
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Applications Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Pending Applications
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : agents.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No pending applications
              </Typography>
              <Typography variant="body2" color="text.secondary">
                All applications have been processed
              </Typography>
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Application #</TableCell>
                      <TableCell>Applicant Name</TableCell>
                      <TableCell>Designation</TableCell>
                      <TableCell>NIC Number</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Submitted Date</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {agents.map((agent) => (
                      <TableRow key={agent.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight={500}>
                            {agent.application_number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {agent.first_name} {agent.last_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {agent.email}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {agent.designation}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {agent.nic_number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {getStatusChip(agent.status)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(agent.updated_at)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={() => handleView(agent)}
                                color="default"
                              >
                                <ViewIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>

                            {agent.status === 'SUBMITTED' && (
                              <>
                                <Tooltip title="Approve">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleApprove(agent)}
                                    color="success"
                                    disabled={processing}
                                  >
                                    <ApproveIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>

                                <Tooltip title="Reject">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleRejectClick(agent)}
                                    color="error"
                                    disabled={processing}
                                  >
                                    <RejectIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} entries
                </Typography>
                <Pagination
                  count={pagination.totalPages}
                  page={pagination.page}
                  onChange={(_, page) => handlePageChange(page)}
                  color="primary"
                  showFirstButton
                  showLastButton
                />
              </Box>
            </>
          )}
        </CardContent>
      </Card>

      {/* Reject Dialog */}
      <Dialog
        open={rejectDialogOpen}
        onClose={() => setRejectDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Reject Application
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Please provide a reason for rejecting this application:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Rejection Reason"
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            placeholder="Enter the reason for rejection..."
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleRejectConfirm}
            color="error"
            variant="contained"
            disabled={!rejectionReason.trim() || processing}
          >
            {processing ? <CircularProgress size={20} /> : 'Reject Application'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ApprovalTray;
