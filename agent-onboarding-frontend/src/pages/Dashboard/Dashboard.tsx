import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  CheckCircle as ApprovalIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { agentService, Agent, AgentFilters } from '../../services/agentService';
import { rolePermissions, statusColors } from '../../theme/theme';
import AgentTable from '../../components/AgentTable/AgentTable';

const statusOptions = [
  { value: 'DRAFT', label: 'Draft' },
  { value: 'PENDING_SUBMISSION', label: 'Pending Submission' },
  { value: 'SUBMITTED', label: 'Submitted' },
  { value: 'APPROVED', label: 'Approved' },
  { value: 'REJECTED', label: 'Rejected' },
];

const searchTypes = [
  { value: 'nic_number', label: 'NIC Number' },
  { value: 'sales_code', label: 'Sales Code' },
  { value: 'application_number', label: 'Application Number' },
];

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [searchType, setSearchType] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const userPermissions = user ? rolePermissions[user.role as keyof typeof rolePermissions] : null;

  const fetchAgents = useCallback(async (filters: AgentFilters = {}) => {
    try {
      setLoading(true);
      setError('');

      const response = await agentService.getAgents({
        ...filters,
        status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
        page: pagination.page,
        limit: pagination.limit,
      });

      if (response.success) {
        setAgents(response.data);
        setPagination(response.pagination);
      } else {
        setError('Failed to fetch agents');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch agents');
    } finally {
      setLoading(false);
    }
  }, [selectedStatuses, pagination.page, pagination.limit]);

  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]);

  const handleStatusChange = (status: string) => {
    setSelectedStatuses(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleSearch = () => {
    if (!searchValue.trim()) return;

    const searchField = searchTypes[searchType].value;
    const filters: AgentFilters = {
      [searchField]: searchValue.trim(),
    };

    fetchAgents(filters);
  };

  const handleClearSearch = () => {
    setSearchValue('');
    fetchAgents();
  };

  const handleCreateNew = () => {
    navigate('/agents/new');
  };

  const handleApprovalTray = () => {
    navigate('/approval-tray');
  };

  const handleEdit = (agent: Agent) => {
    navigate(`/agents/${agent.id}/edit`);
  };

  const handleDelete = async (agent: Agent) => {
    if (window.confirm('Are you sure you want to delete this application?')) {
      try {
        await agentService.deleteAgent(agent.id);
        fetchAgents();
      } catch (err: any) {
        setError(err.message || 'Failed to delete agent');
      }
    }
  };

  const handleSubmit = async (agent: Agent) => {
    try {
      await agentService.submitAgent(agent.id);
      fetchAgents();
    } catch (err: any) {
      setError(err.message || 'Failed to submit agent');
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <Box>
      {/* Filter by Status */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
          Filter by Status
        </Typography>
        <FormGroup row sx={{ gap: 3 }}>
          {statusOptions.map((status) => (
            <FormControlLabel
              key={status.value}
              control={
                <Checkbox
                  checked={selectedStatuses.includes(status.value)}
                  onChange={() => handleStatusChange(status.value)}
                  size="small"
                />
              }
              label={
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {status.label}
                </Typography>
              }
              sx={{ mr: 0 }}
            />
          ))}
        </FormGroup>
      </Box>

      {/* Search Section */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
          Search
        </Typography>
        <Box sx={{ mb: 2 }}>
          <Tabs
            value={searchType}
            onChange={(_, newValue) => setSearchType(newValue)}
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                minHeight: 40,
              },
            }}
          >
            {searchTypes.map((type, index) => (
              <Tab key={type.value} label={type.label} />
            ))}
          </Tabs>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', maxWidth: 600 }}>
          <TextField
            fullWidth
            placeholder="Enter search term..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            size="small"
            InputProps={{
              endAdornment: searchValue && (
                <InputAdornment position="end">
                  <IconButton onClick={handleClearSearch} size="small">
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="contained"
            onClick={handleSearch}
            disabled={!searchValue.trim()}
            sx={{
              minWidth: 100,
              height: 40,
              textTransform: 'none',
              fontWeight: 600,
            }}
          >
            Search
          </Button>
        </Box>
      </Box>

      {/* Action Buttons */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {userPermissions?.canCreate && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateNew}
              sx={{
                textTransform: 'none',
                fontWeight: 600,
                px: 3,
                py: 1,
              }}
            >
              Create New Application
            </Button>
          )}
        </Box>
        <Box>
          {userPermissions?.canViewApprovalTray && (
            <Button
              variant="outlined"
              startIcon={<ApprovalIcon />}
              onClick={handleApprovalTray}
              sx={{
                textTransform: 'none',
                fontWeight: 600,
                px: 3,
                py: 1,
                borderColor: 'primary.main',
                color: 'primary.main',
                '&:hover': {
                  borderColor: 'primary.dark',
                  backgroundColor: 'primary.50',
                },
              }}
            >
              Approval Tray
            </Button>
          )}
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Agents Table */}
      <Box sx={{
        backgroundColor: 'white',
        borderRadius: 2,
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <AgentTable
            agents={agents}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSubmit={handleSubmit}
            pagination={pagination}
            onPageChange={handlePageChange}
            userPermissions={userPermissions}
          />
        )}
      </Box>
    </Box>
  );
};

export default Dashboard;
