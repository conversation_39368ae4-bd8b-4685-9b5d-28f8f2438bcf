import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  CheckCircle as ApprovalIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { agentService, Agent, AgentFilters } from '../../services/agentService';
import { rolePermissions, statusColors } from '../../theme/theme';
import AgentTable from '../../components/AgentTable/AgentTable';

const statusOptions = [
  { value: 'DRAFT', label: 'Draft' },
  { value: 'PENDING_SUBMISSION', label: 'Pending Submission' },
  { value: 'SUBMITTED', label: 'Submitted' },
  { value: 'APPROVED', label: 'Approved' },
  { value: 'REJECTED', label: 'Rejected' },
];

const searchTypes = [
  { value: 'nic_number', label: 'NIC Number' },
  { value: 'sales_code', label: 'Sales Code' },
  { value: 'application_number', label: 'Application Number' },
];

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [searchType, setSearchType] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const userPermissions = user ? rolePermissions[user.role as keyof typeof rolePermissions] : null;

  const fetchAgents = useCallback(async (filters: AgentFilters = {}) => {
    try {
      setLoading(true);
      setError('');

      const response = await agentService.getAgents({
        ...filters,
        status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
        page: pagination.page,
        limit: pagination.limit,
      });

      if (response.success) {
        setAgents(response.data);
        setPagination(response.pagination);
      } else {
        setError('Failed to fetch agents');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch agents');
    } finally {
      setLoading(false);
    }
  }, [selectedStatuses, pagination.page, pagination.limit]);

  useEffect(() => {
    fetchAgents();
  }, [selectedStatuses, pagination.page, fetchAgents]);

  const handleStatusChange = (status: string) => {
    setSelectedStatuses(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handleSearch = () => {
    if (!searchValue.trim()) return;

    const searchField = searchTypes[searchType].value;
    const filters: AgentFilters = {
      [searchField]: searchValue.trim(),
    };

    fetchAgents(filters);
  };

  const handleClearSearch = () => {
    setSearchValue('');
    fetchAgents();
  };

  const handleCreateNew = () => {
    navigate('/agents/new');
  };

  const handleApprovalTray = () => {
    navigate('/approval-tray');
  };

  const handleEdit = (agent: Agent) => {
    navigate(`/agents/${agent.id}/edit`);
  };

  const handleDelete = async (agent: Agent) => {
    if (window.confirm('Are you sure you want to delete this application?')) {
      try {
        await agentService.deleteAgent(agent.id);
        fetchAgents();
      } catch (err: any) {
        setError(err.message || 'Failed to delete agent');
      }
    }
  };

  const handleSubmit = async (agent: Agent) => {
    try {
      await agentService.submitAgent(agent.id);
      fetchAgents();
    } catch (err: any) {
      setError(err.message || 'Failed to submit agent');
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <Box>
      {/* Filter Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Filter by Status
          </Typography>
          <FormGroup row>
            {statusOptions.map((status) => (
              <FormControlLabel
                key={status.value}
                control={
                  <Checkbox
                    checked={selectedStatuses.includes(status.value)}
                    onChange={() => handleStatusChange(status.value)}
                    sx={{
                      color: statusColors[status.value as keyof typeof statusColors],
                      '&.Mui-checked': {
                        color: statusColors[status.value as keyof typeof statusColors],
                      },
                    }}
                  />
                }
                label={status.label}
              />
            ))}
          </FormGroup>
        </CardContent>
      </Card>

      {/* Search Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Search
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Tabs
              value={searchType}
              onChange={(_, newValue) => setSearchType(newValue)}
              variant="scrollable"
              scrollButtons="auto"
            >
              {searchTypes.map((type, index) => (
                <Tab key={type.value} label={type.label} />
              ))}
            </Tabs>
          </Box>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              fullWidth
              placeholder={`Enter ${searchTypes[searchType].label.toLowerCase()}...`}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              InputProps={{
                endAdornment: searchValue && (
                  <InputAdornment position="end">
                    <IconButton onClick={handleClearSearch} size="small">
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Button
              variant="contained"
              onClick={handleSearch}
              disabled={!searchValue.trim()}
              startIcon={<SearchIcon />}
              sx={{ minWidth: 120 }}
            >
              Search
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        {userPermissions?.canCreate && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateNew}
            size="large"
          >
            Create New Application
          </Button>
        )}
        {userPermissions?.canViewApprovalTray && (
          <Button
            variant="outlined"
            startIcon={<ApprovalIcon />}
            onClick={handleApprovalTray}
            size="large"
          >
            Approval Tray
          </Button>
        )}
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Agents Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <AgentTable
              agents={agents}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onSubmit={handleSubmit}
              pagination={pagination}
              onPageChange={handlePageChange}
              userPermissions={userPermissions}
            />
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard;
