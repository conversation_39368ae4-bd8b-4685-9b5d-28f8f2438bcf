import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  TextField,
  Button,
  Alert,
  CircularProgress,
  <PERSON>read<PERSON>rumbs,
  Link,
  Divider,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as BackIcon,
  Send as SubmitIcon,
} from '@mui/icons-material';
import { agentService, Agent } from '../../services/agentService';

interface AgentFormData {
  first_name: string;
  last_name: string;
  nic_number: string;
  email: string;
  phone_number: string;
  date_of_birth: string;
  address: string;
  designation: string;
}

const AgentForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState<AgentFormData>({
    first_name: '',
    last_name: '',
    nic_number: '',
    email: '',
    phone_number: '',
    date_of_birth: '',
    address: '',
    designation: '',
  });

  const [agent, setAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (isEdit && id) {
      fetchAgent(id);
    }
  }, [isEdit, id]);

  const fetchAgent = async (agentId: string) => {
    try {
      setLoading(true);
      const response = await agentService.getAgent(agentId);
      if (response.success) {
        const agentData = response.data;
        setAgent(agentData);
        setFormData({
          first_name: agentData.first_name,
          last_name: agentData.last_name,
          nic_number: agentData.nic_number,
          email: agentData.email,
          phone_number: agentData.phone_number,
          date_of_birth: agentData.date_of_birth.split('T')[0], // Format for date input
          address: agentData.address,
          designation: agentData.designation,
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch agent details');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof AgentFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const validateForm = (): boolean => {
    const requiredFields: (keyof AgentFormData)[] = [
      'first_name',
      'last_name',
      'nic_number',
      'email',
      'phone_number',
      'date_of_birth',
      'address',
      'designation',
    ];

    for (const field of requiredFields) {
      if (!formData[field].trim()) {
        setError(`${field.replace('_', ' ')} is required`);
        return false;
      }
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    // Phone validation
    const phoneRegex = /^\+?[\d\s-()]+$/;
    if (!phoneRegex.test(formData.phone_number)) {
      setError('Please enter a valid phone number');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    setError('');
    setSuccess('');

    if (!validateForm()) return;

    try {
      setSaving(true);

      if (isEdit && id) {
        const response = await agentService.updateAgent(id, formData);
        if (response.success) {
          setSuccess('Agent updated successfully');
          setAgent(response.data);
        }
      } else {
        const response = await agentService.createAgent(formData);
        if (response.success) {
          setSuccess('Agent created successfully');
          setAgent(response.data);
          // Navigate to edit mode after creation
          navigate(`/agents/${response.data.id}/edit`, { replace: true });
        }
      }
    } catch (err: any) {
      setError(err.message || 'Failed to save agent');
    } finally {
      setSaving(false);
    }
  };

  const handleSubmitForApproval = async () => {
    if (!agent) return;

    try {
      setSubmitting(true);
      const response = await agentService.submitAgent(agent.id);
      if (response.success) {
        setSuccess('Agent submitted for approval successfully');
        setAgent(response.data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to submit agent');
    } finally {
      setSubmitting(false);
    }
  };

  const handleBack = () => {
    navigate('/dashboard');
  };

  const canEdit = !agent || agent.status === 'DRAFT' || agent.status === 'PENDING_SUBMISSION';
  const canSubmit = agent && agent.status === 'PENDING_SUBMISSION';

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          component="button"
          variant="body1"
          onClick={handleBack}
          sx={{ textDecoration: 'none' }}
        >
          Dashboard
        </Link>
        <Typography color="text.primary">
          {isEdit ? 'Edit Agent' : 'Create New Agent'}
        </Typography>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<BackIcon />}
          onClick={handleBack}
          sx={{ mr: 2 }}
        >
          Back
        </Button>
        <Typography variant="h4" fontWeight="600">
          {isEdit ? 'Edit Agent Application' : 'Create New Agent Application'}
        </Typography>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Form */}
      <Card>
        <CardContent sx={{ p: 4 }}>
          {agent && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Application Details
              </Typography>
              <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Application Number
                  </Typography>
                  <Typography variant="body1" fontWeight={500}>
                    {agent.application_number}
                  </Typography>
                </Box>
                {agent.sales_code && (
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Sales Code
                    </Typography>
                    <Typography variant="body1" fontWeight={500}>
                      {agent.sales_code}
                    </Typography>
                  </Box>
                )}
              </Box>
              <Divider sx={{ my: 3 }} />
            </Box>
          )}

          <Typography variant="h6" gutterBottom>
            Personal Information
          </Typography>

          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
            gap: 3
          }}>
            <TextField
              fullWidth
              label="First Name"
              value={formData.first_name}
              onChange={handleInputChange('first_name')}
              required
              disabled={!canEdit}
            />
            <TextField
              fullWidth
              label="Last Name"
              value={formData.last_name}
              onChange={handleInputChange('last_name')}
              required
              disabled={!canEdit}
            />
            <TextField
              fullWidth
              label="NIC Number"
              value={formData.nic_number}
              onChange={handleInputChange('nic_number')}
              required
              disabled={!canEdit}
            />
            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              required
              disabled={!canEdit}
            />
            <TextField
              fullWidth
              label="Phone Number"
              value={formData.phone_number}
              onChange={handleInputChange('phone_number')}
              required
              disabled={!canEdit}
            />
            <TextField
              fullWidth
              label="Date of Birth"
              type="date"
              value={formData.date_of_birth}
              onChange={handleInputChange('date_of_birth')}
              InputLabelProps={{ shrink: true }}
              required
              disabled={!canEdit}
            />
            <Box sx={{ gridColumn: { xs: '1', sm: '1 / -1' } }}>
              <TextField
                fullWidth
                label="Address"
                multiline
                rows={3}
                value={formData.address}
                onChange={handleInputChange('address')}
                required
                disabled={!canEdit}
              />
            </Box>
            <TextField
              fullWidth
              label="Designation"
              value={formData.designation}
              onChange={handleInputChange('designation')}
              required
              disabled={!canEdit}
            />
          </Box>

          {/* Action Buttons */}
          <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            {canEdit && (
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSave}
                disabled={saving}
                size="large"
              >
                {saving ? <CircularProgress size={20} /> : 'Save'}
              </Button>
            )}
            {canSubmit && (
              <Button
                variant="outlined"
                startIcon={<SubmitIcon />}
                onClick={handleSubmitForApproval}
                disabled={submitting}
                size="large"
              >
                {submitting ? <CircularProgress size={20} /> : 'Submit for Approval'}
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AgentForm;
