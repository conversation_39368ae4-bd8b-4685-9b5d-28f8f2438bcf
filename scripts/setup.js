#!/usr/bin/env node

/**
 * Setup script for Agent Onboarding API
 * This script helps set up the database and initial data
 */

require('dotenv').config();
const { execSync } = require('child_process');
const { sequelize } = require('../models');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

async function checkDatabaseConnection() {
  try {
    await sequelize.authenticate();
    success('Database connection established');
    return true;
  } catch (err) {
    error(`Database connection failed: ${err.message}`);
    return false;
  }
}

function runCommand(command, description) {
  try {
    info(`Running: ${description}`);
    execSync(command, { stdio: 'inherit' });
    success(`Completed: ${description}`);
    return true;
  } catch (err) {
    error(`Failed: ${description}`);
    console.error(err.message);
    return false;
  }
}

async function setup() {
  log('\n🚀 Agent Onboarding API Setup', colors.bright);
  log('================================\n', colors.bright);

  // Check if .env file exists
  try {
    require('fs').accessSync('.env');
    success('.env file found');
  } catch (err) {
    warning('.env file not found. Please copy .env.example to .env and configure it.');
    process.exit(1);
  }

  // Check database connection
  info('Checking database connection...');
  const dbConnected = await checkDatabaseConnection();
  
  if (!dbConnected) {
    error('Please ensure PostgreSQL is running and .env is configured correctly');
    process.exit(1);
  }

  // Run migrations
  if (!runCommand('npx sequelize-cli db:migrate', 'Database migrations')) {
    process.exit(1);
  }

  // Ask if user wants to seed data
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  readline.question('\nDo you want to seed the database with sample data? (y/N): ', (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      if (runCommand('npx sequelize-cli db:seed:all', 'Database seeding')) {
        success('\n🎉 Setup completed successfully!');
        info('\nSample users created:');
        info('- HOB: <EMAIL> (password: password123)');
        info('- MBD: <EMAIL> (password: password123)');
        info('- SR_MBD: <EMAIL> (password: password123)');
        info('- LOP: <EMAIL> (password: password123)');
        info('- AA: <EMAIL> (password: password123)');
        info('\nYou can now start the server with: npm run dev');
      }
    } else {
      success('\n🎉 Setup completed successfully!');
      info('You can now start the server with: npm run dev');
      warning('Note: No sample data was created. You\'ll need to register users manually.');
    }
    
    readline.close();
    process.exit(0);
  });
}

// Handle cleanup
process.on('SIGINT', async () => {
  log('\n\nSetup interrupted by user', colors.yellow);
  await sequelize.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await sequelize.close();
  process.exit(0);
});

// Run setup
setup().catch(async (err) => {
  error(`Setup failed: ${err.message}`);
  await sequelize.close();
  process.exit(1);
});
