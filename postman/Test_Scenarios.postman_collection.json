{"info": {"_postman_id": "87654321-4321-4321-4321-210987654321", "name": "Agent Onboarding API - Test Scenarios", "description": "Comprehensive test scenarios for different user roles and workflows", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Complete Workflow - HOB User", "item": [{"name": "1. <PERSON><PERSON> as HOB", "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.user.role).to.eql('HOB');", "    pm.environment.set('auth_token', response.data.token);", "    pm.environment.set('user_role', response.data.user.role);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "2. Create Agent Application", "event": [{"listen": "test", "script": {"exec": ["pm.test('Agent created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.status).to.eql('DRAFT');", "    pm.environment.set('agent_id', response.data.id);", "    pm.environment.set('application_number', response.data.application_number);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"nic_number\": \"199512345678\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+1234567890\",\n  \"date_of_birth\": \"1995-03-15\",\n  \"address\": \"456 Oak Avenue, Springfield, IL 62701\",\n  \"designation\": \"Insurance Sales Agent\"\n}"}, "url": {"raw": "{{base_url}}/agents", "host": ["{{base_url}}"], "path": ["agents"]}}}, {"name": "3. Update Agent Application", "event": [{"listen": "test", "script": {"exec": ["pm.test('Agent updated successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.designation).to.eql('Senior Insurance Sales Agent');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"designation\": \"Senior Insurance Sales Agent\",\n  \"phone_number\": \"+1234567891\"\n}"}, "url": {"raw": "{{base_url}}/agents/{{agent_id}}", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}"]}}}, {"name": "4. Try to Approve (Should Fail)", "event": [{"listen": "test", "script": {"exec": ["pm.test('HOB cannot approve', function () {", "    pm.response.to.have.status(403);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{agent_id}}/approve", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}", "approve"]}}}]}, {"name": "Complete Workflow - MBD User", "item": [{"name": "1. <PERSON><PERSON> as MBD", "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data.user.role).to.eql('MBD');", "    pm.environment.set('auth_token', response.data.token);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "2. Create and Submit Agent", "event": [{"listen": "test", "script": {"exec": ["pm.test('Agent created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.environment.set('mbd_agent_id', response.data.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"nic_number\": \"198812345679\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+1234567892\",\n  \"date_of_birth\": \"1988-07-20\",\n  \"address\": \"789 Pine Street, Chicago, IL 60601\",\n  \"designation\": \"Life Insurance Agent\"\n}"}, "url": {"raw": "{{base_url}}/agents", "host": ["{{base_url}}"], "path": ["agents"]}}}, {"name": "3. Submit for Approval", "event": [{"listen": "test", "script": {"exec": ["pm.test('Agent submitted successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.status).to.eql('SUBMITTED');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{mbd_agent_id}}/submit", "host": ["{{base_url}}"], "path": ["agents", "{{mbd_agent_id}}", "submit"]}}}, {"name": "4. <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Approval tray accessible', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/approval-tray", "host": ["{{base_url}}"], "path": ["agents", "approval-tray"]}}}, {"name": "5. Approve Agent", "event": [{"listen": "test", "script": {"exec": ["pm.test('Agent approved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.status).to.eql('APPROVED');", "    pm.expect(response.data.sales_code).to.not.be.null;", "    pm.environment.set('sales_code', response.data.sales_code);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{mbd_agent_id}}/approve", "host": ["{{base_url}}"], "path": ["agents", "{{mbd_agent_id}}", "approve"]}}}]}, {"name": "Search and Filter Tests", "item": [{"name": "Search by Status - Multiple", "event": [{"listen": "test", "script": {"exec": ["pm.test('Multiple status filter works', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?status=APPROVED&status=SUBMITTED", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "status", "value": "APPROVED"}, {"key": "status", "value": "SUBMITTED"}]}}}, {"name": "Search by Sales Code", "event": [{"listen": "test", "script": {"exec": ["pm.test('Sales code search works', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?sales_code={{sales_code}}", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "sales_code", "value": "{{sales_code}}"}]}}}, {"name": "General Search Test", "event": [{"listen": "test", "script": {"exec": ["pm.test('General search works', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?search=Insurance", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "search", "value": "Insurance"}]}}}]}, {"name": "Error <PERSON>", "item": [{"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Invalid login rejected', function () {", "    pm.response.to.have.status(401);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Unauthorized Access", "event": [{"listen": "test", "script": {"exec": ["pm.test('Unauthorized access rejected', function () {", "    pm.response.to.have.status(401);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/agents", "host": ["{{base_url}}"], "path": ["agents"]}}}, {"name": "Invalid Agent Data", "event": [{"listen": "test", "script": {"exec": ["pm.test('Invalid data rejected', function () {", "    pm.response.to.have.status(400);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.false;", "    pm.expect(response.errors).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"\",\n  \"last_name\": \"\",\n  \"nic_number\": \"123\",\n  \"email\": \"invalid-email\",\n  \"phone_number\": \"abc\",\n  \"date_of_birth\": \"invalid-date\"\n}"}, "url": {"raw": "{{base_url}}/agents", "host": ["{{base_url}}"], "path": ["agents"]}}}]}]}