# Agent Onboarding API - Postman Testing Guide

## Overview
This guide provides comprehensive instructions for testing the Agent Onboarding API using the provided Postman collections.

## Files Included

1. **Agent_Onboarding_API.postman_collection.json** - Main API collection with all endpoints
2. **Test_Scenarios.postman_collection.json** - Comprehensive test scenarios for different workflows
3. **Agent_Onboarding_API.postman_environment.json** - Environment variables for testing

## Setup Instructions

### 1. Import Collections and Environment

1. Open Postman
2. Click **Import** button
3. Import all three files:
   - `Agent_Onboarding_API.postman_collection.json`
   - `Test_Scenarios.postman_collection.json`
   - `Agent_Onboarding_API.postman_environment.json`

### 2. Select Environment

1. In the top-right corner, select **Agent Onboarding API - Development** environment
2. Ensure the environment variables are set correctly:
   - `base_url_root`: `http://localhost:3000`
   - `base_url`: `http://localhost:3000/api/v1`

### 3. Start the API Server

Before testing, ensure your API server is running:
```bash
npm run dev
```

## Testing Workflows

### Quick Start Testing

#### 1. Health Check
- Run **System > Health Check** to verify the API is running
- Expected: 200 OK with health status

#### 2. Login with Sample User
- Run **Authentication > Login User** 
- Uses seeded MBD user: `<EMAIL>` / `password123`
- Expected: 200 OK with auth token (automatically saved to environment)

#### 3. Create Agent
- Run **Agent Management > Create Agent Application**
- Expected: 201 Created with agent details (agent_id automatically saved)

#### 4. Get All Agents
- Run **Agent Management > Get All Agents**
- Expected: 200 OK with paginated agent list

### Complete Workflow Testing

#### Scenario 1: HOB User Workflow
Run the **Complete Workflow - HOB User** folder in sequence:
1. Login as HOB user
2. Create agent application
3. Update agent application
4. Try to approve (should fail with 403 Forbidden)

#### Scenario 2: MBD User Workflow
Run the **Complete Workflow - MBD User** folder in sequence:
1. Login as MBD user
2. Create and submit agent
3. Submit for approval
4. Check approval tray
5. Approve agent (generates sales code)

### Search and Filter Testing

#### Test Multiple Status Filters
```
GET /agents?status=APPROVED&status=SUBMITTED
```

#### Test NIC Number Search
```
GET /agents?nic_number=199012345678
```

#### Test Sales Code Search
```
GET /agents?sales_code=SC1702345678001
```

#### Test General Search
```
GET /agents?search=Insurance
```

## User Roles Testing

### Sample Users (from seeders)
- **HOB**: `<EMAIL>` / `password123`
- **MBD**: `<EMAIL>` / `password123`
- **SR_MBD**: `<EMAIL>` / `password123`
- **LOP**: `<EMAIL>` / `password123`
- **AA**: `<EMAIL>` / `password123`

### Permission Testing Matrix

| Action | HOB | MBD | SR_MBD | LOP | AA |
|--------|-----|-----|--------|-----|-----|
| Create Agent | ✅ | ✅ | ✅ | ✅ | ✅ |
| Edit Own Agent | ✅ | ✅ | ✅ | ✅ | ✅ |
| Edit Any Agent | ❌ | ✅ | ✅ | ✅ | ✅ |
| Delete Agent | ✅ | ✅ | ✅ | ✅ | ✅ |
| Submit Agent | ✅ | ✅ | ✅ | ✅ | ✅ |
| Approve Agent | ❌ | ✅ | ✅ | ✅ | ✅ |
| Reject Agent | ❌ | ✅ | ✅ | ✅ | ✅ |
| View Approval Tray | ❌ | ✅ | ✅ | ✅ | ✅ |

## Test Data Examples

### Valid Agent Data
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "nic_number": "199012345678",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "date_of_birth": "1990-05-15",
  "address": "123 Main Street, City, State 12345",
  "designation": "Insurance Agent"
}
```

### Invalid Agent Data (for validation testing)
```json
{
  "first_name": "",
  "last_name": "",
  "nic_number": "123",
  "email": "invalid-email",
  "phone_number": "abc",
  "date_of_birth": "invalid-date"
}
```

## Environment Variables

The following variables are automatically managed by the collection scripts:

- `auth_token` - JWT authentication token
- `refresh_token` - JWT refresh token
- `user_id` - Current user ID
- `user_role` - Current user role
- `agent_id` - Last created agent ID
- `application_number` - Last created application number
- `sales_code` - Last generated sales code

## Expected Response Formats

### Success Response
```json
{
  "success": true,
  "message": "Success message",
  "data": { ... }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": [
    {
      "field": "field_name",
      "message": "Validation error",
      "value": "invalid_value"
    }
  ]
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Success message",
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Common Test Scenarios

### 1. Authentication Flow
1. Register new user
2. Login with credentials
3. Access protected endpoint
4. Refresh token
5. Logout

### 2. Agent Lifecycle
1. Create agent (DRAFT status)
2. Update agent details
3. Submit for approval (SUBMITTED status)
4. Approve/Reject agent
5. Search for agent

### 3. Permission Testing
1. Login as different user roles
2. Try restricted actions
3. Verify proper error responses

### 4. Validation Testing
1. Submit invalid data
2. Verify validation errors
3. Test edge cases

## Troubleshooting

### Common Issues

#### 1. 401 Unauthorized
- Check if auth token is set in environment
- Try logging in again
- Verify token hasn't expired

#### 2. 403 Forbidden
- Check user role permissions
- Verify user has required role for action

#### 3. 404 Not Found
- Check if agent_id is set in environment
- Verify the resource exists

#### 4. 500 Internal Server Error
- Check server logs
- Verify database connection
- Check if all migrations are run

### Debug Tips

1. **Check Environment Variables**: Ensure all required variables are set
2. **View Console**: Check Postman console for script outputs
3. **Check Server Logs**: Monitor server console for errors
4. **Verify Database**: Check if database has required data

## Advanced Testing

### 1. Load Testing
- Use Postman Runner to run collections multiple times
- Test with different user roles simultaneously
- Monitor response times

### 2. Automation
- Set up Newman (Postman CLI) for automated testing
- Integrate with CI/CD pipelines
- Schedule regular test runs

### 3. Data-Driven Testing
- Use CSV files for test data
- Test with various input combinations
- Validate edge cases

## Collection Runner

### Running Complete Test Suite
1. Select **Test Scenarios** collection
2. Click **Run** button
3. Configure iterations and delay
4. Monitor test results

### Running Specific Workflows
1. Select specific folder (e.g., "Complete Workflow - MBD User")
2. Run in sequence
3. Check all tests pass

## Reporting

### Test Results
- Postman provides detailed test results
- Export results for documentation
- Share reports with team

### Performance Metrics
- Monitor response times
- Track success rates
- Identify bottlenecks

---

## Quick Reference

### Essential Endpoints
- Health: `GET /health`
- Login: `POST /auth/login`
- Agents: `GET /agents`
- Create: `POST /agents`
- Approve: `POST /agents/:id/approve`

### Sample cURL Commands
```bash
# Health check
curl http://localhost:3000/api/v1/health

# Login
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get agents
curl -X GET http://localhost:3000/api/v1/agents \
  -H "Authorization: Bearer YOUR_TOKEN"
```

This comprehensive testing setup ensures all API functionality is thoroughly validated across different user roles and scenarios.
