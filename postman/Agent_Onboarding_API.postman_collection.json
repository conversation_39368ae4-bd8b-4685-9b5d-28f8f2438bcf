{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Agent Onboarding API", "description": "Complete API collection for Agent Onboarding Application with authentication, agent management, and approval workflows.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12345678"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('auth_token', response.data.token);", "    pm.environment.set('refresh_token', response.data.refreshToken);", "    pm.environment.set('user_id', response.data.user.id);", "    console.log('User registered successfully');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"TEST001\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\",\n  \"first_name\": \"Test\",\n  \"last_name\": \"User\",\n  \"role\": \"MBD\",\n  \"branch_code\": \"BR001\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('auth_token', response.data.token);", "    pm.environment.set('refresh_token', response.data.refreshToken);", "    pm.environment.set('user_id', response.data.user.id);", "    pm.environment.set('user_role', response.data.user.role);", "    console.log('Login successful');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"Updated\",\n  \"last_name\": \"Name\"\n}"}, "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('auth_token', response.data.token);", "    pm.environment.set('refresh_token', response.data.refreshToken);", "    console.log('To<PERSON> refreshed successfully');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}]}, {"name": "Agent Management", "item": [{"name": "Create Agent Application", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('agent_id', response.data.id);", "    pm.environment.set('application_number', response.data.application_number);", "    console.log('Agent created with ID:', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"nic_number\": \"199012345678\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+1234567890\",\n  \"date_of_birth\": \"1990-05-15\",\n  \"address\": \"123 Main Street, City, State 12345\",\n  \"designation\": \"Insurance Agent\"\n}"}, "url": {"raw": "{{base_url}}/agents", "host": ["{{base_url}}"], "path": ["agents"]}}, "response": []}, {"name": "Get All Agents", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?page=1&limit=10", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get Agents by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?status=DRAFT&status=PENDING_SUBMISSION", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "status", "value": "DRAFT"}, {"key": "status", "value": "PENDING_SUBMISSION"}]}}, "response": []}, {"name": "Search by NIC Number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?nic_number=199012345678", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "nic_number", "value": "199012345678"}]}}, "response": []}, {"name": "Search by Application Number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?application_number={{application_number}}", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "application_number", "value": "{{application_number}}"}]}}, "response": []}, {"name": "General Search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents?search=John", "host": ["{{base_url}}"], "path": ["agents"], "query": [{"key": "search", "value": "<PERSON>"}]}}, "response": []}, {"name": "Get Single Agent", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{agent_id}}", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}"]}}, "response": []}, {"name": "Update Agent Application", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"designation\": \"Senior Insurance Agent\",\n  \"phone_number\": \"+1234567891\"\n}"}, "url": {"raw": "{{base_url}}/agents/{{agent_id}}", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}"]}}, "response": []}]}, {"name": "Approval Workflow", "item": [{"name": "Submit Agent for Approval", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{agent_id}}/submit", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}", "submit"]}}, "response": []}, {"name": "Get Approval Tray", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/approval-tray?page=1&limit=10", "host": ["{{base_url}}"], "path": ["agents", "approval-tray"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Approve Agent Application", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{agent_id}}/approve", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}", "approve"]}}, "response": []}, {"name": "Reject Agent Application", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rejection_reason\": \"Incomplete documentation provided. Please submit all required documents and reapply.\"\n}"}, "url": {"raw": "{{base_url}}/agents/{{agent_id}}/reject", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}", "reject"]}}, "response": []}]}, {"name": "Admin Operations", "item": [{"name": "Delete Agent Application", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/agents/{{agent_id}}", "host": ["{{base_url}}"], "path": ["agents", "{{agent_id}}"]}}, "response": []}]}, {"name": "System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "API Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url_root}}/", "host": ["{{base_url_root}}"], "path": [""]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}