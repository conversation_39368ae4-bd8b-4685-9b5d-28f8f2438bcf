{"id": "12345678-1234-1234-1234-123456789012", "name": "Agent Onboarding API - Development", "values": [{"key": "base_url_root", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "default", "enabled": true}, {"key": "auth_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "user_role", "value": "", "type": "default", "enabled": true}, {"key": "agent_id", "value": "", "type": "default", "enabled": true}, {"key": "application_number", "value": "", "type": "default", "enabled": true}, {"key": "sales_code", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-12-11T10:00:00.000Z", "_postman_exported_using": "Postman/10.20.0"}