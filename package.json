{"name": "agentonboarding-api", "version": "1.0.0", "description": "Agent Onboarding API Service", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node scripts/setup.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo:all", "test": "jest", "test:watch": "jest --watch", "test-setup": "node test-setup.js"}, "keywords": ["express", "api", "agent", "onboarding", "sequelize", "postgresql"], "author": "Product Team", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}