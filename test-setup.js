// Simple test to verify the setup works
require('dotenv').config();

const { sequelize } = require('./models');

async function testSetup() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful!');
    
    // Test models
    const { User, Agent, AuditLog } = require('./models');
    console.log('✅ Models loaded successfully!');
    
    // Test model associations
    console.log('📋 Model associations:');
    console.log('- User associations:', Object.keys(User.associations));
    console.log('- Agent associations:', Object.keys(Agent.associations));
    console.log('- AuditLog associations:', Object.keys(AuditLog.associations));
    
    console.log('✅ All tests passed! Setup is working correctly.');
    
  } catch (error) {
    console.error('❌ Setup test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

testSetup();
