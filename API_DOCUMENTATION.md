# Agent Onboarding API Documentation

## Base URL
```
http://localhost:3000/api/v1
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Response Format
All responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "message": "Success message",
  "data": { ... }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "errors": [
    {
      "field": "field_name",
      "message": "Validation error",
      "value": "invalid_value"
    }
  ]
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Success message",
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Endpoints

### Authentication

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "user_id": "HOB001",
  "email": "<EMAIL>",
  "password": "Password123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "role": "HOB",
  "branch_code": "BR001"
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password123"
}
```

#### Get Profile
```http
GET /auth/profile
Authorization: Bearer <token>
```

#### Logout
```http
POST /auth/logout
Authorization: Bearer <token>
```

### Agents

#### Get All Agents
```http
GET /agents?status=APPROVED&page=1&limit=10&search=john
Authorization: Bearer <token>
```

Query Parameters:
- `status`: Filter by status (DRAFT, PENDING_SUBMISSION, SUBMITTED, APPROVED, REJECTED)
- `nic_number`: Search by exact NIC number
- `sales_code`: Search by exact sales code
- `application_number`: Search by exact application number
- `search`: General search across name, email, designation
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

#### Get Single Agent
```http
GET /agents/:id
Authorization: Bearer <token>
```

#### Create Agent Application
```http
POST /agents
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Alice",
  "last_name": "Cooper",
  "nic_number": "199012345678",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "date_of_birth": "1990-05-15",
  "address": "123 Main Street, City, State 12345",
  "designation": "Insurance Agent"
}
```

#### Update Agent Application
```http
PUT /agents/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Alice Updated",
  "designation": "Senior Insurance Agent"
}
```

#### Delete Agent Application
```http
DELETE /agents/:id
Authorization: Bearer <token>
```

#### Submit Agent for Approval
```http
POST /agents/:id/submit
Authorization: Bearer <token>
```

#### Approve Agent Application
```http
POST /agents/:id/approve
Authorization: Bearer <token>
```
*Requires MBD, SR_MBD, LOP, or AA role*

#### Reject Agent Application
```http
POST /agents/:id/reject
Authorization: Bearer <token>
Content-Type: application/json

{
  "rejection_reason": "Incomplete documentation provided"
}
```
*Requires MBD, SR_MBD, LOP, or AA role*

#### Get Approval Tray
```http
GET /agents/approval-tray?page=1&limit=10
Authorization: Bearer <token>
```
*Requires MBD, SR_MBD, LOP, or AA role*

### Health Check
```http
GET /health
```

## Status Codes

- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests
- `500` - Internal Server Error

## User Roles & Permissions

| Action | HOB | MBD | SR_MBD | LOP | AA |
|--------|-----|-----|--------|-----|-----|
| Create Agent | ✓ | ✓ | ✓ | ✓ | ✓ |
| Edit Own Agent | ✓ | ✓ | ✓ | ✓ | ✓ |
| Edit Any Agent | ✗ | ✓ | ✓ | ✓ | ✓ |
| Delete Agent | ✓ | ✓ | ✓ | ✓ | ✓ |
| Submit Agent | ✓ | ✓ | ✓ | ✓ | ✓ |
| Approve Agent | ✗ | ✓ | ✓ | ✓ | ✓ |
| Reject Agent | ✗ | ✓ | ✓ | ✓ | ✓ |
| View Approval Tray | ✗ | ✓ | ✓ | ✓ | ✓ |

## Application Status Flow

```
DRAFT → PENDING_SUBMISSION → SUBMITTED → APPROVED/REJECTED
```

### Status Descriptions
- **DRAFT**: Initial state, can be edited
- **PENDING_SUBMISSION**: Ready for submission, can be edited
- **SUBMITTED**: Submitted for approval, cannot be edited
- **APPROVED**: Approved with sales code generated
- **REJECTED**: Rejected with reason, cannot be edited

## Rate Limiting
- 100 requests per 15 minutes per IP address
- Applies to all endpoints

## Error Handling

### Validation Errors
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Please provide a valid email address",
      "value": "invalid-email"
    }
  ]
}
```

### Authentication Errors
```json
{
  "success": false,
  "message": "Invalid or expired token"
}
```

### Authorization Errors
```json
{
  "success": false,
  "message": "Insufficient permissions"
}
```

## Sample Workflow

1. **Register/Login** to get authentication token
2. **Create Agent Application** with personal details
3. **Update Application** if needed (while in DRAFT/PENDING_SUBMISSION)
4. **Submit Application** for approval
5. **Approver reviews** in approval tray
6. **Approve/Reject** application with reason if rejected

## Testing with cURL

### Login and get token
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Create agent application
```bash
curl -X POST http://localhost:3000/api/v1/agents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "nic_number": "123456789",
    "email": "<EMAIL>",
    "phone_number": "+1234567890",
    "date_of_birth": "1990-01-01",
    "address": "123 Main St",
    "designation": "Agent"
  }'
```
